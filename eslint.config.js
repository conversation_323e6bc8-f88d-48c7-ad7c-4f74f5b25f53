// @see https://eslint.bootcss.com/docs/rules/
import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginVue from "eslint-plugin-vue";
import pluginPrettier from "eslint-plugin-prettier";
import configPrettier from "eslint-config-prettier";

export default [
  // 忽略文件配置
  {
    ignores: [
      "**/node_modules/**",
      "dist/**",
      "build/**",
      "coverage/**",
      "*.d.ts",
      "**/*.md",
      "**/*.log"
    ]
  },

  // 基础 JavaScript 配置
  js.configs.recommended,

  // TypeScript 配置
  ...tseslint.configs.recommended,

  // Vue 配置
  ...pluginVue.configs["flat/essential"],

  // 全局配置
  {
    files: ["**/*.{js,mjs,cjs,ts,mts,cts,vue}"],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.es2021,
        ...globals.node,
        ...globals.jest,
      },
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      prettier: pluginPrettier,
    },
    rules: {
      // ESLint 基础规则 (https://eslint.bootcss.com/docs/rules/)
      "no-var": "error", // 要求使用 let 或 const 而不是 var
      "no-multiple-empty-lines": ["warn", { max: 1 }], // 不允许多个空行
      "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
      "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
      "no-unexpected-multiline": "error", // 禁止空余的多行
      "no-useless-escape": "off", // 禁止不必要的转义字符

      // TypeScript 规则 (https://typescript-eslint.io/rules)
      "@typescript-eslint/no-unused-vars": "error", // 禁止定义未使用的变量
      "@typescript-eslint/prefer-ts-expect-error": "error", // 禁止使用 @ts-ignore
      "@typescript-eslint/no-explicit-any": "off", // 禁止使用 any 类型
      "@typescript-eslint/no-non-null-assertion": "off",
      "@typescript-eslint/no-namespace": "off", // 禁止使用自定义 TypeScript 模块和命名空间
      "@typescript-eslint/semi": "off",

      // Vue 规则 (https://eslint.vuejs.org/rules/)
      "vue/multi-word-component-names": "off", // 要求组件名称始终为 "-" 链接的单词
      // 注意: vue/script-setup-uses-vars 规则在 eslint-plugin-vue v10.0.0 中已被移除，不再需要
      "vue/no-mutating-props": "off", // 不允许组件 prop的改变
      "vue/attribute-hyphenation": "off", // 对模板中的自定义组件强制执行属性命名样式

      // Prettier 规则
      "prettier/prettier": "error",
    },
  },

  // Vue 文件特殊配置
  {
    files: ["**/*.vue"],
    languageOptions: {
      parserOptions: {
        parser: tseslint.parser,
        jsxPragma: "React",
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
  },

  // Prettier 配置 - 必须放在最后以覆盖冲突的规则
  configPrettier,
];
