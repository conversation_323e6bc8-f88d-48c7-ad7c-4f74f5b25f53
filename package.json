{"name": "vue-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint src", "fix": "eslint src --fix"}, "dependencies": {"vue": "^3.4.35"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@eslint/js": "latest", "@vitejs/plugin-vue": "^5.1.2", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.4.0", "globals": "^16.3.0", "typescript": "^5.5.3", "typescript-eslint": "^8.38.0", "vite": "^5.4.0", "vue-tsc": "^2.0.29"}}